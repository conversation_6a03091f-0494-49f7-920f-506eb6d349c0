"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/dashboard/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state from localStorage on app start\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = ()=>{\n            try {\n                const storedToken = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - Initializing auth...\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - Token:\", storedToken ? \"EXISTS\" : \"MISSING\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - User:\", storedUser ? \"EXISTS\" : \"MISSING\");\n                if (storedToken && storedUser) {\n                    console.log(\"✅ AuthContext - Setting user and token from localStorage\");\n                    setToken(storedToken);\n                    setUser(JSON.parse(storedUser));\n                } else {\n                    console.log(\"❌ AuthContext - No stored auth data found\");\n                }\n            } catch (error) {\n                console.error(\"❌ AuthContext - Error initializing auth:\", error);\n                // Clear corrupted data\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"refreshToken\");\n                localStorage.removeItem(\"user\");\n            } finally{\n                console.log(\"\\uD83D\\uDD0D AuthContext - Setting isLoading to false\");\n                setIsLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = (newToken, refreshToken, userData)=>{\n        setToken(newToken);\n        setUser(userData);\n        // Store in localStorage\n        localStorage.setItem(\"token\", newToken);\n        localStorage.setItem(\"refreshToken\", refreshToken);\n        localStorage.setItem(\"user\", JSON.stringify(userData));\n    };\n    const logout = ()=>{\n        setToken(null);\n        setUser(null);\n        // Clear localStorage\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        // Redirect to login\n        router.push(\"/auth/login\");\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!token && !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"UhpF3G6ZIIK9LkChMeraiQOms44=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});