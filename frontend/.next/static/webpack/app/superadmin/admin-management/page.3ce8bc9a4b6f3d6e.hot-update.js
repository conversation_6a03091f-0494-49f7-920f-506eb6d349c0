"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/admin-management/page",{

/***/ "(app-pages-browser)/./hooks/useAdminAuth.ts":
/*!*******************************!*\
  !*** ./hooks/useAdminAuth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdminAuth: function() { return /* binding */ useAdminAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useAdminAuth auto */ \n\n\nfunction useAdminAuth() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { requiredRole = \"admin\", redirectTo } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Don't redirect if still loading\n        if (isLoading) {\n            return;\n        }\n        // Check if user is authenticated\n        if (!user) {\n            router.push(\"/auth/login\");\n            return;\n        }\n        // Check role-based access\n        if (requiredRole === \"super_admin\" && user.role !== \"super_admin\") {\n            // Redirect non-super_admin users to appropriate dashboard\n            if (user.role === \"admin\") {\n                router.push(redirectTo || \"/admin/dashboard\");\n            } else {\n                router.push(redirectTo || \"/auth/login\");\n            }\n            return;\n        }\n        // Admin pages should only be accessible by admin role (not super_admin)\n        if (requiredRole === \"admin\" && user.role !== \"admin\") {\n            if (user.role === \"super_admin\") {\n                router.push(\"/superadmin/dashboard\");\n            } else {\n                router.push(redirectTo || \"/auth/login\");\n            }\n            return;\n        }\n    }, [\n        user,\n        isLoading,\n        requiredRole,\n        redirectTo,\n        router\n    ]);\n    return {\n        user,\n        isAuthenticated: !!user,\n        isAuthorized: user && (requiredRole === \"super_admin\" ? user.role === \"super_admin\" : requiredRole === \"admin\" ? user.role === \"admin\" : true),\n        loading: !user\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useAdminAuth.ts\n"));

/***/ })

});