"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/superadmin/admin-management/page",{

/***/ "(app-pages-browser)/./hooks/useAdminAuth.ts":
/*!*******************************!*\
  !*** ./hooks/useAdminAuth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAdminAuth: function() { return /* binding */ useAdminAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useAdminAuth auto */ \n\n\nfunction useAdminAuth() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { requiredRole = \"admin\", redirectTo } = options;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D useAdminAuth - User:\", user ? \"EXISTS\" : \"NULL\");\n        console.log(\"\\uD83D\\uDD0D useAdminAuth - IsLoading:\", isLoading);\n        console.log(\"\\uD83D\\uDD0D useAdminAuth - RequiredRole:\", requiredRole);\n        // Don't redirect if still loading\n        if (isLoading) {\n            console.log(\"\\uD83D\\uDD0D useAdminAuth - Still loading, waiting...\");\n            return;\n        }\n        // Check if user is authenticated\n        if (!user) {\n            console.log(\"❌ useAdminAuth - No user found, redirecting to login\");\n            router.push(\"/auth/login\");\n            return;\n        }\n        // Check role-based access\n        if (requiredRole === \"super_admin\" && user.role !== \"super_admin\") {\n            // Redirect non-super_admin users to appropriate dashboard\n            if (user.role === \"admin\") {\n                router.push(redirectTo || \"/admin/dashboard\");\n            } else {\n                router.push(redirectTo || \"/auth/login\");\n            }\n            return;\n        }\n        // Admin pages should only be accessible by admin role (not super_admin)\n        if (requiredRole === \"admin\" && user.role !== \"admin\") {\n            if (user.role === \"super_admin\") {\n                router.push(\"/superadmin/dashboard\");\n            } else {\n                router.push(redirectTo || \"/auth/login\");\n            }\n            return;\n        }\n    }, [\n        user,\n        requiredRole,\n        redirectTo,\n        router\n    ]);\n    return {\n        user,\n        isAuthenticated: !!user,\n        isAuthorized: user && (requiredRole === \"super_admin\" ? user.role === \"super_admin\" : requiredRole === \"admin\" ? user.role === \"admin\" : true),\n        loading: !user\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useAdminAuth.ts\n"));

/***/ })

});