"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/dashboard/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Initialize auth state from localStorage on app start\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initAuth = ()=>{\n            try {\n                const storedToken = localStorage.getItem(\"token\");\n                const storedUser = localStorage.getItem(\"user\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - Initializing auth...\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - Token:\", storedToken ? \"EXISTS\" : \"MISSING\");\n                console.log(\"\\uD83D\\uDD0D AuthContext - User:\", storedUser ? \"EXISTS\" : \"MISSING\");\n                if (storedToken && storedUser) {\n                    console.log(\"✅ AuthContext - Setting user and token from localStorage\");\n                    setToken(storedToken);\n                    setUser(JSON.parse(storedUser));\n                } else {\n                    console.log(\"❌ AuthContext - No stored auth data found\");\n                }\n            } catch (error) {\n                console.error(\"❌ AuthContext - Error initializing auth:\", error);\n                // Clear corrupted data\n                localStorage.removeItem(\"token\");\n                localStorage.removeItem(\"refreshToken\");\n                localStorage.removeItem(\"user\");\n            } finally{\n                console.log(\"\\uD83D\\uDD0D AuthContext - Setting isLoading to false\");\n                setIsLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    const login = (newToken, refreshToken, userData)=>{\n        console.log(\"\\uD83D\\uDD0D AuthContext - Login called with:\", {\n            token: newToken ? \"EXISTS\" : \"MISSING\",\n            refreshToken: refreshToken ? \"EXISTS\" : \"MISSING\",\n            user: userData\n        });\n        setToken(newToken);\n        setUser(userData);\n        // Store in localStorage\n        localStorage.setItem(\"token\", newToken);\n        localStorage.setItem(\"refreshToken\", refreshToken);\n        localStorage.setItem(\"user\", JSON.stringify(userData));\n        console.log(\"✅ AuthContext - Auth data stored successfully\");\n    };\n    const logout = ()=>{\n        setToken(null);\n        setUser(null);\n        // Clear localStorage\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"refreshToken\");\n        localStorage.removeItem(\"user\");\n        // Redirect to login\n        router.push(\"/auth/login\");\n    };\n    const value = {\n        user,\n        token,\n        isLoading,\n        login,\n        logout,\n        isAuthenticated: !!token && !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/brid1/frontend/contexts/AuthContext.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"UhpF3G6ZIIK9LkChMeraiQOms44=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});