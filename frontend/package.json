{"name": "auction-tender-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:auth": "jest --testPathPattern=authentication", "test:protected-route": "jest --testPathPattern=protected-route", "test:role-access": "jest --testPathPattern=role-access-control", "test:all": "npm run test && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "lucide-react": "^0.292.0", "next": "^14.2.30", "react": "^18", "react-day-picker": "^9.8.0", "react-dom": "^18", "react-hook-form": "^7.48.2", "recharts": "^3.1.0", "socket.io-client": "^4.7.4", "sonner": "^2.0.6", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}