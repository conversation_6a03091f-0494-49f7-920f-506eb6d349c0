/**
 * ProtectedRoute Component Tests
 * Tests for route protection, token validation, and authorization logic
 */

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import ProtectedRoute from '@/components/ProtectedRoute'
import { useToast } from '@/hooks/use-toast'
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: jest.fn(),
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Test component to wrap in ProtectedRoute
function TestProtectedComponent() {
  return <div data-testid="protected-content">Protected Content</div>
}

describe('ProtectedRoute Component', () => {
  const mockPush = jest.fn()
  const mockToast = jest.fn()
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
  const mockUseToast = useToast as jest.MockedFunction<typeof useToast>

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)
    
    mockUseToast.mockReturnValue({
      toast: mockToast,
    } as any)
  })

  describe('Authentication Checks', () => {
    it('should redirect to login when no token is present', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'غير مصرح',
        description: 'يجب تسجيل الدخول للوصول لهذه الصفحة',
        variant: 'destructive'
      })

      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('should redirect to login when no user data is present', async () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid-jwt-token.with.three.parts'
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'غير مصرح',
        description: 'يجب تسجيل الدخول للوصول لهذه الصفحة',
        variant: 'destructive'
      })
    })

    it('should redirect to login when token format is invalid', async () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'invalid-token-format'
        if (key === 'user') return JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
          status: 'approved',
          profile: { fullName: 'Test User' }
        })
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'جلسة غير صالحة',
        description: 'يرجى تسجيل الدخول مرة أخرى',
        variant: 'destructive'
      })

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')
    })

    it('should render protected content when authentication is valid', async () => {
      const validUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Test User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(validUser)
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument()
      })

      expect(mockPush).not.toHaveBeenCalled()
      expect(mockToast).not.toHaveBeenCalled()
    })
  })

  describe('Role-Based Authorization', () => {
    it('should allow access when user has required role', async () => {
      const adminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(adminUser)
        return null
      })

      render(
        <ProtectedRoute requiredRole="admin">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(screen.getByTestId('protected-content')).toBeInTheDocument()
      })

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should redirect when user does not have required role', async () => {
      const regularUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'user',
        status: 'approved',
        profile: { fullName: 'Regular User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(regularUser)
        return null
      })

      render(
        <ProtectedRoute requiredRole="admin">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/unauthorized')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'غير مصرح',
        description: 'ليس لديك صلاحية للوصول لهذه الصفحة',
        variant: 'destructive'
      })
    })

    it('should redirect admin to superadmin dashboard when accessing superadmin routes', async () => {
      const adminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(adminUser)
        return null
      })

      render(
        <ProtectedRoute requiredRole="super_admin">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/admin/dashboard')
      })
    })

    it('should redirect superadmin to admin dashboard when accessing admin routes', async () => {
      const superAdminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'super_admin',
        status: 'approved',
        profile: { fullName: 'Super Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(superAdminUser)
        return null
      })

      render(
        <ProtectedRoute requiredRole="admin">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/superadmin/dashboard')
      })
    })
  })

  describe('User Status Validation', () => {
    it('should redirect when user status is not approved', async () => {
      const pendingUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'pending',
        profile: { fullName: 'Pending User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(pendingUser)
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/pending-approval')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'حساب في انتظار الموافقة',
        description: 'حسابك في انتظار موافقة المدير',
        variant: 'destructive'
      })
    })

    it('should redirect when user is suspended', async () => {
      const suspendedUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'suspended',
        profile: { fullName: 'Suspended User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(suspendedUser)
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/suspended')
      })

      expect(mockToast).toHaveBeenCalledWith({
        title: 'حساب معلق',
        description: 'تم تعليق حسابك. يرجى التواصل مع المدير',
        variant: 'destructive'
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle corrupted user data gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return 'invalid-json-data'
        return null
      })

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })

      expect(consoleSpy).toHaveBeenCalled()
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')

      consoleSpy.mockRestore()
    })

    it('should handle missing user properties gracefully', async () => {
      const incompleteUser = {
        id: '1',
        email: '<EMAIL>'
        // Missing role, status, profile
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(incompleteUser)
        return null
      })

      render(
        <ProtectedRoute>
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })
    })
  })

  describe('Custom Redirect Paths', () => {
    it('should use custom redirect path when provided', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      render(
        <ProtectedRoute redirectTo="/custom-login">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/custom-login')
      })
    })

    it('should use custom unauthorized redirect when provided', async () => {
      const regularUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'user',
        status: 'approved',
        profile: { fullName: 'Regular User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'valid.jwt.token'
        if (key === 'user') return JSON.stringify(regularUser)
        return null
      })

      render(
        <ProtectedRoute requiredRole="admin" unauthorizedRedirect="/custom-unauthorized">
          <TestProtectedComponent />
        </ProtectedRoute>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/custom-unauthorized')
      })
    })
  })
})
