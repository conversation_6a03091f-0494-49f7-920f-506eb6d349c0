/**
 * End-to-End Authentication Flow Tests
 * Comprehensive E2E tests simulating real user authentication workflows
 */

import { test, expect } from '@playwright/test'

// Test data
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
}

const SUPERADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'SuperAdmin123!'
}

const BASE_URL = process.env.BASE_URL || 'http://localhost:3002'

test.describe('Authentication E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Clear localStorage before each test
    await page.goto(BASE_URL)
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  })

  test.describe('Login Flow', () => {
    test('should successfully login as admin user', async ({ page }) => {
      // Navigate to login page
      await page.goto(`${BASE_URL}/auth/login`)
      
      // Verify login page loads
      await expect(page.locator('h1')).toContainText('تسجيل الدخول')
      
      // Fill login form
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Wait for redirect to admin dashboard
      await page.waitForURL(`${BASE_URL}/admin/dashboard`, { timeout: 10000 })
      
      // Verify admin dashboard loads
      await expect(page.locator('h1')).toContainText('لوحة التحكم')
      
      // Verify admin sidebar is present
      await expect(page.locator('[data-testid="admin-sidebar"]')).toBeVisible()
      
      // Verify user is authenticated
      const token = await page.evaluate(() => localStorage.getItem('token'))
      expect(token).toBeTruthy()
      
      const user = await page.evaluate(() => localStorage.getItem('user'))
      expect(user).toBeTruthy()
      
      const userData = JSON.parse(user!)
      expect(userData.role).toBe('admin')
    })

    test('should successfully login as superadmin user', async ({ page }) => {
      // Navigate to login page
      await page.goto(`${BASE_URL}/auth/login`)
      
      // Fill login form
      await page.fill('input[type="email"]', SUPERADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', SUPERADMIN_CREDENTIALS.password)
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Wait for redirect to superadmin dashboard
      await page.waitForURL(`${BASE_URL}/superadmin/dashboard`, { timeout: 10000 })
      
      // Verify superadmin dashboard loads
      await expect(page.locator('h1')).toContainText('لوحة تحكم المدير العام')
      
      // Verify superadmin sidebar is present
      await expect(page.locator('[data-testid="superadmin-sidebar"]')).toBeVisible()
      
      // Verify user is authenticated
      const token = await page.evaluate(() => localStorage.getItem('token'))
      expect(token).toBeTruthy()
      
      const user = await page.evaluate(() => localStorage.getItem('user'))
      expect(user).toBeTruthy()
      
      const userData = JSON.parse(user!)
      expect(userData.role).toBe('super_admin')
    })

    test('should show error for invalid credentials', async ({ page }) => {
      await page.goto(`${BASE_URL}/auth/login`)
      
      // Fill with invalid credentials
      await page.fill('input[type="email"]', '<EMAIL>')
      await page.fill('input[type="password"]', 'wrongpassword')
      
      // Submit form
      await page.click('button[type="submit"]')
      
      // Should remain on login page
      await expect(page).toHaveURL(`${BASE_URL}/auth/login`)
      
      // Should show error message (if implemented)
      // await expect(page.locator('[data-testid="error-message"]')).toBeVisible()
    })
  })

  test.describe('Role-Based Access Control', () => {
    test('admin user should not access superadmin routes', async ({ page }) => {
      // Login as admin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/admin/dashboard`)
      
      // Try to access superadmin route
      await page.goto(`${BASE_URL}/superadmin/dashboard`)
      
      // Should be redirected to admin dashboard
      await page.waitForURL(`${BASE_URL}/admin/dashboard`, { timeout: 5000 })
      
      // Verify we're on admin dashboard, not superadmin
      await expect(page.locator('h1')).toContainText('لوحة التحكم')
      await expect(page.locator('h1')).not.toContainText('لوحة تحكم المدير العام')
    })

    test('superadmin user should not access admin routes', async ({ page }) => {
      // Login as superadmin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', SUPERADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', SUPERADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/superadmin/dashboard`)
      
      // Try to access admin route
      await page.goto(`${BASE_URL}/admin/dashboard`)
      
      // Should be redirected to superadmin dashboard
      await page.waitForURL(`${BASE_URL}/superadmin/dashboard`, { timeout: 5000 })
      
      // Verify we're on superadmin dashboard, not admin
      await expect(page.locator('h1')).toContainText('لوحة تحكم المدير العام')
      await expect(page.locator('h1')).not.toContainText('لوحة التحكم')
    })

    test('unauthenticated user should be redirected to login', async ({ page }) => {
      // Try to access protected admin route without authentication
      await page.goto(`${BASE_URL}/admin/dashboard`)
      
      // Should be redirected to login
      await page.waitForURL(`${BASE_URL}/auth/login`, { timeout: 5000 })
      
      // Try to access protected superadmin route without authentication
      await page.goto(`${BASE_URL}/superadmin/dashboard`)
      
      // Should be redirected to login
      await page.waitForURL(`${BASE_URL}/auth/login`, { timeout: 5000 })
    })
  })

  test.describe('Session Management', () => {
    test('should maintain session across page refreshes', async ({ page }) => {
      // Login as admin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/admin/dashboard`)
      
      // Refresh the page
      await page.reload()
      
      // Should still be on admin dashboard
      await expect(page).toHaveURL(`${BASE_URL}/admin/dashboard`)
      await expect(page.locator('h1')).toContainText('لوحة التحكم')
      
      // Verify tokens are still present
      const token = await page.evaluate(() => localStorage.getItem('token'))
      expect(token).toBeTruthy()
    })

    test('should handle logout correctly', async ({ page }) => {
      // Login as admin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/admin/dashboard`)
      
      // Click logout button
      await page.click('[data-testid="logout-button"]')
      
      // Should be redirected to login page
      await page.waitForURL(`${BASE_URL}/auth/login`, { timeout: 5000 })
      
      // Verify tokens are cleared
      const token = await page.evaluate(() => localStorage.getItem('token'))
      expect(token).toBeNull()
      
      const user = await page.evaluate(() => localStorage.getItem('user'))
      expect(user).toBeNull()
    })

    test('should handle expired tokens gracefully', async ({ page }) => {
      // Manually set an invalid token
      await page.goto(BASE_URL)
      await page.evaluate(() => {
        localStorage.setItem('token', 'invalid-token')
        localStorage.setItem('user', JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
          status: 'approved',
          profile: { fullName: 'Test User' }
        }))
      })
      
      // Try to access protected route
      await page.goto(`${BASE_URL}/admin/dashboard`)
      
      // Should be redirected to login due to invalid token
      await page.waitForURL(`${BASE_URL}/auth/login`, { timeout: 5000 })
      
      // Verify tokens are cleared
      const token = await page.evaluate(() => localStorage.getItem('token'))
      expect(token).toBeNull()
    })
  })

  test.describe('Navigation and Sidebar Tests', () => {
    test('admin sidebar should not contain superadmin items', async ({ page }) => {
      // Login as admin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/admin/dashboard`)
      
      // Verify admin sidebar items are present
      await expect(page.locator('text=لوحة التحكم')).toBeVisible()
      await expect(page.locator('text=المناقصات')).toBeVisible()
      await expect(page.locator('text=المستخدمين')).toBeVisible()
      
      // Verify superadmin items are NOT present
      await expect(page.locator('text=التحليلات والتقارير')).not.toBeVisible()
      await expect(page.locator('text=إدارة المدراء')).not.toBeVisible()
      await expect(page.locator('text=إدارة الأمان')).not.toBeVisible()
    })

    test('superadmin sidebar should contain superadmin-specific items', async ({ page }) => {
      // Login as superadmin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', SUPERADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', SUPERADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/superadmin/dashboard`)
      
      // Verify superadmin sidebar items are present
      await expect(page.locator('text=لوحة التحكم')).toBeVisible()
      await expect(page.locator('text=التحليلات والتقارير')).toBeVisible()
      await expect(page.locator('text=إدارة المدراء')).toBeVisible()
      await expect(page.locator('text=إدارة الأمان')).toBeVisible()
      await expect(page.locator('text=إعدادات المنصة')).toBeVisible()
      
      // Verify search and favorites are NOT present (as per user preference)
      await expect(page.locator('text=البحث')).not.toBeVisible()
      await expect(page.locator('text=المفضلة')).not.toBeVisible()
    })
  })

  test.describe('Performance and Race Condition Tests', () => {
    test('should handle rapid navigation without race conditions', async ({ page }) => {
      // Login as superadmin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', SUPERADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', SUPERADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      await page.waitForURL(`${BASE_URL}/superadmin/dashboard`)
      
      // Rapidly navigate between pages
      await page.goto(`${BASE_URL}/superadmin/admin-management`)
      await page.goto(`${BASE_URL}/superadmin/analytics`)
      await page.goto(`${BASE_URL}/superadmin/dashboard`)
      
      // Should end up on dashboard without being redirected to login
      await expect(page).toHaveURL(`${BASE_URL}/superadmin/dashboard`)
      await expect(page.locator('h1')).toContainText('لوحة تحكم المدير العام')
    })

    test('should handle page load timing issues', async ({ page }) => {
      // Simulate slow network conditions
      await page.route('**/*', route => {
        setTimeout(() => route.continue(), 100)
      })
      
      // Login as admin
      await page.goto(`${BASE_URL}/auth/login`)
      await page.fill('input[type="email"]', ADMIN_CREDENTIALS.email)
      await page.fill('input[type="password"]', ADMIN_CREDENTIALS.password)
      await page.click('button[type="submit"]')
      
      // Should still successfully navigate to dashboard despite slow loading
      await page.waitForURL(`${BASE_URL}/admin/dashboard`, { timeout: 15000 })
      await expect(page.locator('h1')).toContainText('لوحة التحكم')
    })
  })
})
