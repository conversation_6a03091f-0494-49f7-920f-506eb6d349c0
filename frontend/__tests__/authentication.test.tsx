/**
 * Comprehensive Authentication System Tests
 * Tests for login, token management, session handling, and race condition prevention
 */

import React from 'react'
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { authAPI } from '@/lib/api'
import '@testing-library/jest-dom'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock API
jest.mock('@/lib/api', () => ({
  authAPI: {
    login: jest.fn(),
    logout: jest.fn(),
  },
}))

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Test component to access AuthContext
function TestAuthComponent() {
  const { user, token, isLoading, login, logout, isAuthenticated } = useAuth()
  
  return (
    <div>
      <div data-testid="user">{user ? JSON.stringify(user) : 'null'}</div>
      <div data-testid="token">{token || 'null'}</div>
      <div data-testid="isLoading">{isLoading.toString()}</div>
      <div data-testid="isAuthenticated">{isAuthenticated.toString()}</div>
      <button 
        data-testid="login-btn" 
        onClick={() => login('test-token', 'test-refresh', {
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
          status: 'approved',
          profile: { fullName: 'Test User' }
        })}
      >
        Login
      </button>
      <button data-testid="logout-btn" onClick={logout}>Logout</button>
    </div>
  )
}

// Test component for useAdminAuth hook
function TestAdminAuthComponent({ requiredRole }: { requiredRole: 'admin' | 'super_admin' }) {
  const { user, isAuthenticated, isAuthorized, loading } = useAdminAuth({ requiredRole })
  
  return (
    <div>
      <div data-testid="admin-user">{user ? JSON.stringify(user) : 'null'}</div>
      <div data-testid="admin-authenticated">{isAuthenticated.toString()}</div>
      <div data-testid="admin-authorized">{isAuthorized.toString()}</div>
      <div data-testid="admin-loading">{loading.toString()}</div>
    </div>
  )
}

describe('Authentication System', () => {
  const mockPush = jest.fn()
  const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
  const mockAuthAPI = authAPI as jest.Mocked<typeof authAPI>

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)
  })

  describe('AuthContext', () => {
    it('should initialize with loading state', () => {
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )

      expect(screen.getByTestId('isLoading')).toHaveTextContent('true')
      expect(screen.getByTestId('isAuthenticated')).toHaveTextContent('false')
    })

    it('should load user from localStorage on initialization', async () => {
      const mockUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Test User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'stored-token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('isLoading')).toHaveTextContent('false')
      })

      expect(screen.getByTestId('token')).toHaveTextContent('stored-token')
      expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockUser))
      expect(screen.getByTestId('isAuthenticated')).toHaveTextContent('true')
    })

    it('should handle corrupted localStorage data gracefully', async () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'stored-token'
        if (key === 'user') return 'invalid-json'
        return null
      })

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('isLoading')).toHaveTextContent('false')
      })

      expect(consoleSpy).toHaveBeenCalledWith('Error initializing auth:', expect.any(Error))
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')

      consoleSpy.mockRestore()
    })

    it('should handle login correctly', async () => {
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('isLoading')).toHaveTextContent('false')
      })

      fireEvent.click(screen.getByTestId('login-btn'))

      await waitFor(() => {
        expect(screen.getByTestId('isAuthenticated')).toHaveTextContent('true')
      })

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('token', 'test-token')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshToken', 'test-refresh')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', expect.any(String))
    })

    it('should handle logout correctly', async () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'stored-token'
        if (key === 'user') return JSON.stringify({
          id: '1',
          email: '<EMAIL>',
          role: 'admin',
          status: 'approved',
          profile: { fullName: 'Test User' }
        })
        return null
      })

      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('isAuthenticated')).toHaveTextContent('true')
      })

      fireEvent.click(screen.getByTestId('logout-btn'))

      await waitFor(() => {
        expect(screen.getByTestId('isAuthenticated')).toHaveTextContent('false')
      })

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')
      expect(mockPush).toHaveBeenCalledWith('/auth/login')
    })
  })

  describe('useAdminAuth Hook', () => {
    it('should wait for AuthContext to load before making decisions', async () => {
      // Mock AuthContext to be loading initially
      const TestComponent = () => {
        const [isLoading, setIsLoading] = React.useState(true)
        const [user, setUser] = React.useState(null)

        React.useEffect(() => {
          setTimeout(() => {
            setUser({
              id: '1',
              email: '<EMAIL>',
              role: 'admin',
              status: 'approved',
              profile: { fullName: 'Admin User' }
            })
            setIsLoading(false)
          }, 100)
        }, [])

        return (
          <AuthProvider>
            <TestAdminAuthComponent requiredRole="admin" />
          </AuthProvider>
        )
      }

      render(<TestComponent />)

      // Should not redirect immediately while loading
      expect(mockPush).not.toHaveBeenCalled()

      await waitFor(() => {
        expect(screen.getByTestId('admin-loading')).toHaveTextContent('false')
      }, { timeout: 200 })
    })

    it('should redirect unauthenticated users to login', async () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      render(
        <AuthProvider>
          <TestAdminAuthComponent requiredRole="admin" />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/auth/login')
      })
    })

    it('should allow admin users to access admin routes', async () => {
      const adminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'admin-token'
        if (key === 'user') return JSON.stringify(adminUser)
        return null
      })

      render(
        <AuthProvider>
          <TestAdminAuthComponent requiredRole="admin" />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('admin-authorized')).toHaveTextContent('true')
      })

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should redirect super_admin users away from admin routes', async () => {
      const superAdminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'super_admin',
        status: 'approved',
        profile: { fullName: 'Super Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'superadmin-token'
        if (key === 'user') return JSON.stringify(superAdminUser)
        return null
      })

      render(
        <AuthProvider>
          <TestAdminAuthComponent requiredRole="admin" />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/superadmin/dashboard')
      })
    })

    it('should allow super_admin users to access superadmin routes', async () => {
      const superAdminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'super_admin',
        status: 'approved',
        profile: { fullName: 'Super Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'superadmin-token'
        if (key === 'user') return JSON.stringify(superAdminUser)
        return null
      })

      render(
        <AuthProvider>
          <TestAdminAuthComponent requiredRole="super_admin" />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('admin-authorized')).toHaveTextContent('true')
      })

      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should redirect admin users away from superadmin routes', async () => {
      const adminUser = {
        id: '1',
        email: '<EMAIL>',
        role: 'admin',
        status: 'approved',
        profile: { fullName: 'Admin User' }
      }

      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'token') return 'admin-token'
        if (key === 'user') return JSON.stringify(adminUser)
        return null
      })

      render(
        <AuthProvider>
          <TestAdminAuthComponent requiredRole="super_admin" />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/admin/dashboard')
      })
    })
  })
})
