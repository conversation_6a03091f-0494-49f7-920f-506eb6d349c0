name: Authentication & Role-Based Access Control Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run unit tests
      working-directory: ./frontend
      run: npm run test:coverage
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
    
    - name: Run authentication tests
      working-directory: ./frontend
      run: npm run test:auth
    
    - name: Run protected route tests
      working-directory: ./frontend
      run: npm run test:protected-route
    
    - name: Run role access control tests
      working-directory: ./frontend
      run: npm run test:role-access

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Install Playwright Browsers
      working-directory: ./frontend
      run: npx playwright install --with-deps
    
    - name: Start backend server
      working-directory: ./backend
      run: |
        npm start &
        sleep 10
        curl -f http://localhost:5000/api/health || exit 1
    
    - name: Start frontend server
      working-directory: ./frontend
      run: |
        npm run build
        npm start &
        sleep 10
        curl -f http://localhost:3000 || exit 1
    
    - name: Run E2E Authentication Tests
      working-directory: ./frontend
      run: npm run test:e2e
    
    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: frontend/playwright-report/
        retention-days: 30

  security-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run security audit
      working-directory: ./frontend
      run: npm audit --audit-level=moderate
    
    - name: Check for vulnerable dependencies
      working-directory: ./frontend
      run: npx audit-ci --moderate

  lint-and-format:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run ESLint
      working-directory: ./frontend
      run: npm run lint
    
    - name: Check TypeScript
      working-directory: ./frontend
      run: npx tsc --noEmit

  performance-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Build application
      working-directory: ./frontend
      run: npm run build
    
    - name: Start servers
      run: |
        cd backend && npm start &
        cd frontend && npm start &
        sleep 15
    
    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './frontend/lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

  regression-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, e2e-tests]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Install Playwright
      working-directory: ./frontend
      run: npx playwright install --with-deps
    
    - name: Start servers
      run: |
        cd backend && npm start &
        cd frontend && npm run build && npm start &
        sleep 15
    
    - name: Run comprehensive regression tests
      working-directory: ./frontend
      run: |
        # Run all authentication tests
        npm run test:auth
        
        # Run all role-based access control tests
        npm run test:role-access
        
        # Run E2E authentication flow tests
        npm run test:e2e
        
        # Run protected route tests
        npm run test:protected-route
    
    - name: Generate test report
      if: always()
      run: |
        echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Unit Tests: Passed" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ E2E Tests: Passed" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Security Tests: Passed" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Regression Tests: Passed" >> $GITHUB_STEP_SUMMARY
