'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import DashboardLayout from '@/components/DashboardLayout'
import api from '@/lib/api'
import { useAdminAuth } from '@/hooks/useAdminAuth'
import { 
  ArrowLeft,
  User,
  Building,
  Mail,
  Phone,
  Calendar,
  Shield,
  Activity,
  Edit,
  Save,
  Ban,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Gavel,
  FileText,
  DollarSign,
  Clock
} from 'lucide-react'

interface UserData {
  _id: string
  email: string
  role: string
  status: string
  isVerified: boolean
  createdAt: string
  lastLogin?: string
  profile: {
    fullName?: string
    companyName?: string
    governmentEntity?: string
    phone?: string
    address?: string
    website?: string
    description?: string
  }
  stats: {
    auctionsCreated?: number
    tendersCreated?: number
    bidsPlaced?: number
    applicationsSubmitted?: number
    totalSpent?: number
    totalEarned?: number
  }
  activities: Array<{
    _id: string
    type: string
    description: string
    timestamp: string
  }>
}

export default function AdminUserDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user: currentUser } = useAdminAuth({ requiredRole: 'admin' })
  const [user, setUser] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(true)
  const [editing, setEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    role: '',
    status: '',
    fullName: '',
    companyName: '',
    governmentEntity: '',
    phone: '',
    address: '',
    website: '',
    description: ''
  })

  useEffect(() => {
    loadUser()
  }, [params.id])

  const loadUser = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/admin/users/${params.id}`)
      
      if (response.data.success) {
        const userData = response.data.data
        setUser(userData)
        setFormData({
          role: userData.role,
          status: userData.status,
          fullName: userData.profile.fullName || '',
          companyName: userData.profile.companyName || '',
          governmentEntity: userData.profile.governmentEntity || '',
          phone: userData.profile.phone || '',
          address: userData.profile.address || '',
          website: userData.profile.website || '',
          description: userData.profile.description || ''
        })
      } else {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على المستخدم',
          variant: 'destructive'
        })
        router.push('/admin/users')
      }
    } catch (error) {
      console.error('Error loading user:', error)
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ في تحميل بيانات المستخدم',
        variant: 'destructive'
      })
      router.push('/admin/users')
    } finally {
      setLoading(false)
    }
  }

  // Helper function to check if current user can manage the target user
  const canManageUser = (): boolean => {
    if (!user || !currentUser) return false
    // Regular admin users cannot manage super admin users
    if (currentUser.role === 'admin' && user.role === 'super_admin') {
      return false
    }
    return true
  }

  const saveUser = async () => {
    try {
      setSaving(true)
      const response = await api.put(`/admin/users/${params.id}`, {
        role: formData.role,
        status: formData.status,
        profile: {
          fullName: formData.fullName,
          companyName: formData.companyName,
          governmentEntity: formData.governmentEntity,
          phone: formData.phone,
          address: formData.address,
          website: formData.website,
          description: formData.description
        }
      })
      
      if (response.data.success) {
        setUser({ ...user!, ...response.data.data })
        setEditing(false)
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث بيانات المستخدم بنجاح'
        })
      }
    } catch (error: any) {
      toast({
        title: 'خطأ في التحديث',
        description: error.response?.data?.message || 'حدث خطأ في تحديث البيانات',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const toggleUserStatus = async () => {
    const newStatus = user?.status === 'active' ? 'suspended' : 'active'
    try {
      const response = await api.patch(`/admin/users/${params.id}/status`, {
        status: newStatus
      })
      
      if (response.data.success) {
        setUser({ ...user!, status: newStatus })
        toast({
          title: 'تم التحديث',
          description: `تم ${newStatus === 'active' ? 'تفعيل' : 'تعليق'} المستخدم`
        })
      }
    } catch (error: any) {
      toast({
        title: 'خطأ',
        description: error.response?.data?.message || 'حدث خطأ في تحديث الحالة',
        variant: 'destructive'
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 0
    }).format(price)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>
      case 'suspended':
        return <Badge variant="destructive">معلق</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">في الانتظار</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRoleName = (role: string) => {
    const roles = {
      'user': 'مستخدم',
      'company': 'شركة',
      'government': 'جهة حكومية',
      'admin': 'مدير'
    }
    return roles[role as keyof typeof roles] || role
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'auction_created':
      case 'bid_placed':
        return <Gavel className="h-4 w-4 text-blue-600" />
      case 'tender_created':
      case 'application_submitted':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'login':
        return <User className="h-4 w-4 text-gray-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <DashboardLayout allowedRoles={['admin']}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل بيانات المستخدم...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!user) {
    return (
      <DashboardLayout allowedRoles={['admin']}>
        <div className="text-center py-12">
          <AlertTriangle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">المستخدم غير موجود</h2>
          <p className="text-gray-600 mb-6">لم يتم العثور على المستخدم المطلوب</p>
          <Button onClick={() => router.push('/admin/users')}>
            العودة لقائمة المستخدمين
          </Button>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout allowedRoles={['admin']}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => router.push('/admin/users')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              العودة للمستخدمين
            </Button>
            <div>
              <h1 className="text-3xl font-bold">تفاصيل المستخدم</h1>
              <p className="text-gray-600">إدارة ومراجعة بيانات المستخدم</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge(user.status)}
            {canManageUser() ? (
              <Button
                onClick={toggleUserStatus}
                variant={user.status === 'active' ? 'destructive' : 'default'}
              >
                {user.status === 'active' ? (
                  <>
                    <Ban className="h-4 w-4 ml-2" />
                    تعليق المستخدم
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 ml-2" />
                    تفعيل المستخدم
                  </>
                )}
              </Button>
            ) : (
              <div className="flex items-center text-sm text-gray-500">
                <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-xs font-medium">
                  مدير عام - وصول محدود
                </span>
              </div>
            )}
          </div>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">الملف الشخصي</TabsTrigger>
            <TabsTrigger value="stats">الإحصائيات</TabsTrigger>
            <TabsTrigger value="activity">النشاط</TabsTrigger>
            <TabsTrigger value="settings">الإعدادات</TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <User className="h-5 w-5" />
                        المعلومات الشخصية
                      </CardTitle>
                      {canManageUser() && (
                        <Button
                          variant="outline"
                          onClick={() => editing ? saveUser() : setEditing(true)}
                          disabled={saving}
                        >
                          {saving ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 ml-2"></div>
                          ) : editing ? (
                            <Save className="h-4 w-4 ml-2" />
                          ) : (
                            <Edit className="h-4 w-4 ml-2" />
                          )}
                          {editing ? 'حفظ' : 'تعديل'}
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="email">البريد الإلكتروني</Label>
                        <Input
                          id="email"
                          value={user.email}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                      <div>
                        <Label htmlFor="role">الدور</Label>
                        {editing && canManageUser() ? (
                          <Select value={formData.role} onValueChange={(value) => setFormData({...formData, role: value})}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">مستخدم</SelectItem>
                              <SelectItem value="company">شركة</SelectItem>
                              <SelectItem value="government">جهة حكومية</SelectItem>
                              <SelectItem value="admin">مدير</SelectItem>
                            </SelectContent>
                          </Select>
                        ) : (
                          <Input value={getRoleName(user.role)} disabled className="bg-gray-50" />
                        )}
                      </div>
                      
                      {user.role === 'user' && (
                        <div>
                          <Label htmlFor="fullName">الاسم الكامل</Label>
                          <Input
                            id="fullName"
                            value={formData.fullName}
                            onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                            disabled={!editing || !canManageUser()}
                            className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                          />
                        </div>
                      )}
                      
                      {user.role === 'company' && (
                        <div>
                          <Label htmlFor="companyName">اسم الشركة</Label>
                          <Input
                            id="companyName"
                            value={formData.companyName}
                            onChange={(e) => setFormData({...formData, companyName: e.target.value})}
                            disabled={!editing || !canManageUser()}
                            className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                          />
                        </div>
                      )}
                      
                      {user.role === 'government' && (
                        <div>
                          <Label htmlFor="governmentEntity">الجهة الحكومية</Label>
                          <Input
                            id="governmentEntity"
                            value={formData.governmentEntity}
                            onChange={(e) => setFormData({...formData, governmentEntity: e.target.value})}
                            disabled={!editing || !canManageUser()}
                            className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                          />
                        </div>
                      )}
                      
                      <div>
                        <Label htmlFor="phone">الهاتف</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          disabled={!editing || !canManageUser()}
                          className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="website">الموقع الإلكتروني</Label>
                        <Input
                          id="website"
                          value={formData.website}
                          onChange={(e) => setFormData({...formData, website: e.target.value})}
                          disabled={!editing || !canManageUser()}
                          className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="address">العنوان</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                        disabled={!editing || !canManageUser()}
                        className={(!editing || !canManageUser()) ? "bg-gray-50" : ""}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="description">الوصف</Label>
                      <textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        disabled={!editing || !canManageUser()}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${(!editing || !canManageUser()) ? "bg-gray-50" : ""}`}
                        rows={3}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>معلومات الحساب</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">الحالة:</span>
                      <span className="font-medium">{getStatusBadge(user.status)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">التحقق:</span>
                      <span className="font-medium">
                        {user.isVerified ? (
                          <Badge className="bg-green-100 text-green-800">محقق</Badge>
                        ) : (
                          <Badge variant="destructive">غير محقق</Badge>
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">تاريخ التسجيل:</span>
                      <span className="font-medium">{new Date(user.createdAt).toLocaleDateString('ar-SA')}</span>
                    </div>
                    {user.lastLogin && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">آخر دخول:</span>
                        <span className="font-medium">{new Date(user.lastLogin).toLocaleDateString('ar-SA')}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Stats Tab */}
          <TabsContent value="stats">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {user.role === 'company' && (
                <>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">المزادات المنشأة</p>
                          <p className="text-3xl font-bold text-blue-600">{user.stats.auctionsCreated || 0}</p>
                        </div>
                        <Gavel className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">إجمالي الأرباح</p>
                          <p className="text-3xl font-bold text-green-600">{formatPrice(user.stats.totalEarned || 0)}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
              
              {user.role === 'government' && (
                <>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">المناقصات المنشأة</p>
                          <p className="text-3xl font-bold text-green-600">{user.stats.tendersCreated || 0}</p>
                        </div>
                        <FileText className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
              
              {user.role === 'user' && (
                <>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">المزايدات المقدمة</p>
                          <p className="text-3xl font-bold text-blue-600">{user.stats.bidsPlaced || 0}</p>
                        </div>
                        <Gavel className="h-8 w-8 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">طلبات المناقصات</p>
                          <p className="text-3xl font-bold text-green-600">{user.stats.applicationsSubmitted || 0}</p>
                        </div>
                        <FileText className="h-8 w-8 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600">إجمالي الإنفاق</p>
                          <p className="text-3xl font-bold text-purple-600">{formatPrice(user.stats.totalSpent || 0)}</p>
                        </div>
                        <DollarSign className="h-8 w-8 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </TabsContent>

          {/* Activity Tab */}
          <TabsContent value="activity">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  سجل النشاط
                </CardTitle>
                <CardDescription>
                  آخر الأنشطة والعمليات التي قام بها المستخدم
                </CardDescription>
              </CardHeader>
              <CardContent>
                {user.activities && user.activities.length > 0 ? (
                  <div className="space-y-4">
                    {user.activities.map((activity) => (
                      <div key={activity._id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                        <div className="mt-1">
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{activity.description}</p>
                          <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(activity.timestamp)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    <p className="text-gray-600">لا يوجد نشاط مسجل</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  إعدادات المستخدم
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">حالة الحساب</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">نشط</SelectItem>
                      <SelectItem value="suspended">معلق</SelectItem>
                      <SelectItem value="pending">في الانتظار</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button onClick={saveUser} disabled={saving}>
                    {saving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    ) : (
                      <Save className="h-4 w-4 ml-2" />
                    )}
                    حفظ الإعدادات
                  </Button>
                  <Button variant="outline" onClick={() => setFormData({
                    role: user.role,
                    status: user.status,
                    fullName: user.profile.fullName || '',
                    companyName: user.profile.companyName || '',
                    governmentEntity: user.profile.governmentEntity || '',
                    phone: user.profile.phone || '',
                    address: user.profile.address || '',
                    website: user.profile.website || '',
                    description: user.profile.description || ''
                  })}>
                    إعادة تعيين
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
